import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Video from '@/models/Video';
import { requireAuth } from '@/lib/auth';
import { withCors } from '@/lib/cors';
import slugify from 'slugify';

// GET /api/videos - Get all videos with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const search = searchParams.get('search');

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (category) query.category = category;
    if (featured !== null) query.featured = featured === 'true';
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get videos with pagination
    const videos = await Video.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await Video.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: videos,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get videos error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// POST /api/videos - Create new video
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      id,
      title,
      description,
      category,
      tags = [],
      featured = false,
      status = 'draft',
      order = 0,
    } = data;

    // Validate required fields
    if (!id || !title) {
      return NextResponse.json(
        { error: 'Video ID and title are required' },
        { status: 400 }
      );
    }

    // Check if video ID already exists
    const existingVideo = await Video.findOne({ id });
    if (existingVideo) {
      return NextResponse.json(
        { error: 'Video with this ID already exists' },
        { status: 400 }
      );
    }

    // Generate slug
    const baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    while (await Video.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create video
    const video = new Video({
      id,
      title,
      slug,
      description,
      category,
      tags,
      featured,
      status,
      order,
    });

    await video.save();

    return NextResponse.json({
      success: true,
      data: video,
    }, { status: 201 });
  } catch (error) {
    console.error('Create video error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
