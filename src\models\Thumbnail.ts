import mongoose from 'mongoose';

const ThumbnailSchema = new mongoose.Schema({
  videoId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Basic YouTube video ID validation (11 characters, alphanumeric + - and _)
        return /^[a-zA-Z0-9_-]{11}$/.test(v);
      },
      message: 'Invalid YouTube video ID format'
    }
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  category: {
    type: String,
    trim: true,
    maxlength: 50
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 30
  }],
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  order: {
    type: Number,
    default: 0
  },
  thumbnailUrl: {
    type: String,
    default: function() {
      return `https://img.youtube.com/vi/${this.videoId}/maxresdefault.jpg`;
    }
  },
  embedUrl: {
    type: String,
    default: function() {
      return `https://www.youtube.com/embed/${this.videoId}`;
    }
  }
}, {
  timestamps: true
});

// Create indexes for better performance
ThumbnailSchema.index({ status: 1, order: 1 });
ThumbnailSchema.index({ featured: 1, status: 1 });
ThumbnailSchema.index({ videoId: 1 });

// Pre-save middleware to generate URLs
ThumbnailSchema.pre('save', function(next) {
  if (this.videoId) {
    this.thumbnailUrl = `https://img.youtube.com/vi/${this.videoId}/maxresdefault.jpg`;
    this.embedUrl = `https://www.youtube.com/embed/${this.videoId}`;
  }
  next();
});

export default mongoose.models.Thumbnail || mongoose.model('Thumbnail', ThumbnailSchema);
