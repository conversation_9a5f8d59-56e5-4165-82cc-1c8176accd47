import mongoose from 'mongoose';
import slugify from 'slugify';

const ThumbnailSchema = new mongoose.Schema({
  videoId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Basic YouTube video ID validation (11 characters, alphanumeric + - and _)
        return /^[a-zA-Z0-9_-]{11}$/.test(v);
      },
      message: 'Invalid YouTube video ID format'
    }
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  slug: {
    type: String,
    required: false, // Make optional for existing records
    unique: true,
    trim: true,
    lowercase: true,
    maxlength: 250,
    sparse: true // Allow null values in unique index
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  category: {
    type: String,
    trim: true,
    maxlength: 50
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 30
  }],
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  order: {
    type: Number,
    default: 0
  },
  thumbnailUrl: {
    type: String,
    default: function() {
      return `https://img.youtube.com/vi/${this.videoId}/maxresdefault.jpg`;
    }
  },
  embedUrl: {
    type: String,
    default: function() {
      return `https://www.youtube.com/embed/${this.videoId}`;
    }
  }
}, {
  timestamps: true
});

// Create indexes for better performance
ThumbnailSchema.index({ status: 1, order: 1 });
ThumbnailSchema.index({ featured: 1, status: 1 });
ThumbnailSchema.index({ videoId: 1 });
ThumbnailSchema.index({ slug: 1 });

// Pre-save middleware to generate URLs and slug
ThumbnailSchema.pre('save', async function(next) {
  if (this.videoId) {
    this.thumbnailUrl = `https://img.youtube.com/vi/${this.videoId}/maxresdefault.jpg`;
    this.embedUrl = `https://www.youtube.com/embed/${this.videoId}`;
  }

  // Generate slug if title is modified, this is a new document, or slug is missing
  if (this.isModified('title') || this.isNew || !this.slug) {
    const baseSlug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    const ThumbnailModel = this.constructor as mongoose.Model<any>;

    while (await ThumbnailModel.findOne({ slug, _id: { $ne: this._id } })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    this.slug = slug;
  }

  next();
});

export default mongoose.models.Thumbnail || mongoose.model('Thumbnail', ThumbnailSchema);
