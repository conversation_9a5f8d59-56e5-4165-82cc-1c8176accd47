'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';
import ImageUpload from '@/components/ImageUpload';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Loader2, Star, Eye, User } from 'lucide-react';
import Link from 'next/link';

interface Testimonial {
  _id: string;
  clientName: string;
  clientPhoto?: string;
  clientPosition?: string;
  clientCompany?: string;
  content: string;
  rating: number;
  projectType?: string;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  createdAt: string;
  updatedAt: string;
}

export default function EditTestimonialPage() {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    avatar: '',
    role: '',
    company: '',
    content: '',
    rating: 5,
    email: '',
    linkedinUrl: '',
    featured: false,
    status: 'draft' as 'draft' | 'published' | 'archived',
    order: 0,
  });

  useEffect(() => {
    if (params.id) {
      fetchTestimonial();
    }
  }, [params.id]);

  const fetchTestimonial = async () => {
    try {
      const response = await fetch(`/api/testimonials/${params.id}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        const testimonial = data.data;
        setFormData({
          name: testimonial.name || '',
          avatar: testimonial.avatar || '',
          role: testimonial.role || '',
          company: testimonial.company || '',
          content: testimonial.content || '',
          rating: testimonial.rating || 5,
          email: testimonial.email || '',
          linkedinUrl: testimonial.linkedinUrl || '',
          featured: testimonial.featured || false,
          status: testimonial.status || 'draft',
          order: testimonial.order || 0,
        });
      } else {
        setError('Failed to fetch testimonial');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (status: 'draft' | 'published') => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`/api/testimonials/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          status,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Testimonial ${status === 'published' ? 'published' : 'updated'} successfully!`);
        setTimeout(() => {
          router.push('/dashboard/testimonials');
        }, 1500);
      } else {
        setError(data.error || 'Failed to update testimonial');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-warning fill-current' : 'text-muted-foreground'
        }`}
      />
    ));
  };

  if (fetchLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading testimonial...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/testimonials">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Testimonials
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Testimonial</h1>
              <p className="text-gray-600">Update testimonial information</p>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Person Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter person's name..."
                    required
                  />
                </div>

                <div>
                  <ImageUpload
                    label="Photo (Optional)"
                    value={formData.avatar}
                    onChange={(url) => handleInputChange('avatar', url)}
                    folder="testimonials"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="role">Role/Position *</Label>
                    <Input
                      id="role"
                      value={formData.role}
                      onChange={(e) => handleInputChange('role', e.target.value)}
                      placeholder="e.g., CEO, Marketing Director"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      placeholder="e.g., Tech Corp, Creative Agency"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                    <Input
                      id="linkedinUrl"
                      value={formData.linkedinUrl}
                      onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}
                      placeholder="https://linkedin.com/in/username"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Testimonial Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="content">Testimonial Content *</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    placeholder="Enter the testimonial content..."
                    rows={6}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="rating">Rating *</Label>
                  <select
                    id="rating"
                    value={formData.rating}
                    onChange={(e) => handleInputChange('rating', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    required
                  >
                    <option value={5}>5 Stars - Excellent</option>
                    <option value={4}>4 Stars - Very Good</option>
                    <option value={3}>3 Stars - Good</option>
                    <option value={2}>2 Stars - Fair</option>
                    <option value={1}>1 Star - Poor</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 xl:sticky xl:top-6 xl:h-fit">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                  <Label htmlFor="featured">Featured Testimonial</Label>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={() => handleSubmit('draft')}
                    variant="outline"
                    className="w-full"
                    disabled={loading || !formData.name || !formData.role || !formData.content}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                    Save as Draft
                  </Button>

                  <Button
                    onClick={() => handleSubmit('published')}
                    className="w-full"
                    disabled={loading || !formData.name || !formData.role || !formData.content}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Eye className="mr-2 h-4 w-4" />}
                    Update & Publish
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-6 rounded-lg border">
                  <div className="space-y-4">
                    {/* Rating */}
                    <div className="flex items-center space-x-1">
                      {renderStars(formData.rating)}
                    </div>

                    {/* Content */}
                    <blockquote className="text-gray-700 italic">
                      "{formData.content || 'Testimonial content will appear here...'}"
                    </blockquote>

                    {/* Person Info */}
                    <div className="flex items-center space-x-3 pt-4 border-t">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                        {formData.avatar ? (
                          <img
                            src={formData.avatar}
                            alt={formData.name || 'Person'}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-gray-400" />
                        )}
                      </div>

                      <div>
                        <div className="font-medium text-sm">
                          {formData.name || 'Person Name'}
                        </div>
                        {(formData.role || formData.company) && (
                          <div className="text-xs text-gray-500">
                            {[formData.role, formData.company]
                              .filter(Boolean)
                              .join(' at ')}
                          </div>
                        )}
                      </div>
                    </div>

                    {formData.featured && (
                      <div className="pt-2">
                        <Badge className="text-xs bg-yellow-100 text-yellow-800">
                          Featured
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
