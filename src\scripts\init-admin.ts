import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword } from '@/lib/auth';

async function initializeAdmin() {
  try {
    await connectDB();
    
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    
    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: adminEmail });
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      return;
    }
    
    // Create admin user
    const hashedPassword = await hashPassword(adminPassword);
    
    const adminUser = new User({
      email: adminEmail,
      password: hashedPassword,
      name: 'Admin User',
      role: 'admin',
      isActive: true,
    });
    
    await adminUser.save();
    
    console.log('✅ Admin user created successfully');
    console.log(`📧 Email: ${adminEmail}`);
    console.log(`🔑 Password: ${adminPassword}`);
    console.log('🚀 You can now login to the CMS');
    
  } catch (error) {
    console.error('❌ Error initializing admin user:', error);
  } finally {
    process.exit(0);
  }
}

initializeAdmin();
