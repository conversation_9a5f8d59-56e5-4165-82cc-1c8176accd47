import mongoose, { Document, Schema } from "mongoose";

export interface IShort extends Document {
  id: string;
  title: string;
  slug: string;
  description?: string;
  platform: "youtube";
  embedUrl?: string;
  featured: boolean;
  status: "draft" | "published" | "archived";
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const ShortSchema = new Schema<IShort>(
  {
    id: {
      type: String,
      required: true,
      trim: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    description: {
      type: String,
      maxlength: 500,
    },
    platform: {
      type: String,
      enum: ["youtube"],
      required: true,
      default: "youtube",
    },
    embedUrl: {
      type: String,
    },
    featured: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: ["draft", "published", "archived"],
      default: "draft",
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance (slug already has unique index)
ShortSchema.index({ status: 1, order: 1 });
ShortSchema.index({ platform: 1 });
ShortSchema.index({ featured: 1 });

// Pre-save middleware to auto-generate slug and embedUrl
ShortSchema.pre("save", function (next) {
  if (!this.slug && this.title) {
    const slugify = require("slugify");
    this.slug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
  }

  // Auto-generate embed URL for YouTube Shorts
  if (this.id && this.platform === "youtube" && !this.embedUrl) {
    this.embedUrl = `https://www.youtube.com/embed/${this.id}`;
  }

  next();
});

export default mongoose.models.Short ||
  mongoose.model<IShort>("Short", ShortSchema);
