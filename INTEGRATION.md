# Portfolio CMS Integration Guide

This guide shows how to integrate the Portfolio CMS with your Next.js portfolio website.

## 🔗 API Endpoints for Portfolio Integration

### Public Blog API

The CMS provides public API endpoints that your portfolio can use to fetch published content:

#### Get All Published Blog Posts
```
GET http://localhost:3002/api/public/blog
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Posts per page (default: 10)
- `featured` (optional): Filter featured posts (`true`/`false`)
- `category` (optional): Filter by category

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "...",
      "title": "My Blog Post",
      "slug": "my-blog-post",
      "excerpt": "This is a brief description...",
      "category": "Tutorial",
      "tags": ["nextjs", "react"],
      "featured": true,
      "thumbnail": "https://example.com/image.jpg",
      "author": "Uttam Rimal",
      "publishedAt": "2024-01-01T00:00:00.000Z",
      "readTime": 5,
      "views": 123,
      "seoTitle": "SEO Title",
      "seoDescription": "SEO Description"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

#### Get Single Blog Post by Slug
```
GET http://localhost:3002/api/public/blog/[slug]
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "...",
    "title": "My Blog Post",
    "slug": "my-blog-post",
    "excerpt": "Brief description...",
    "content": "<p>Full HTML content...</p>",
    "category": "Tutorial",
    "tags": ["nextjs", "react"],
    "featured": true,
    "thumbnail": "https://example.com/image.jpg",
    "author": "Uttam Rimal",
    "publishedAt": "2024-01-01T00:00:00.000Z",
    "readTime": 5,
    "views": 124,
    "seoTitle": "SEO Title",
    "seoDescription": "SEO Description",
    "seoKeywords": ["nextjs", "tutorial"]
  }
}
```

## 🚀 Integration Examples

### 1. Fetch Blog Posts in Your Portfolio

Create a utility function to fetch blog posts:

```typescript
// lib/cms.ts
const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3002';

export interface BlogPost {
  _id: string;
  title: string;
  slug: string;
  excerpt: string;
  content?: string;
  category: string;
  tags: string[];
  featured: boolean;
  thumbnail?: string;
  author: string;
  publishedAt: string;
  readTime: number;
  views: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export interface BlogResponse {
  success: boolean;
  data: BlogPost[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export async function getBlogPosts(params?: {
  page?: number;
  limit?: number;
  featured?: boolean;
  category?: string;
}): Promise<BlogResponse> {
  const searchParams = new URLSearchParams();
  
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.featured !== undefined) searchParams.set('featured', params.featured.toString());
  if (params?.category) searchParams.set('category', params.category);

  const response = await fetch(`${CMS_BASE_URL}/api/public/blog?${searchParams}`, {
    next: { revalidate: 300 } // Revalidate every 5 minutes
  });

  if (!response.ok) {
    throw new Error('Failed to fetch blog posts');
  }

  return response.json();
}

export async function getBlogPost(slug: string): Promise<{ success: boolean; data: BlogPost }> {
  const response = await fetch(`${CMS_BASE_URL}/api/public/blog/${slug}`, {
    next: { revalidate: 300 }
  });

  if (!response.ok) {
    throw new Error('Failed to fetch blog post');
  }

  return response.json();
}
```

### 2. Update Your Portfolio Blog Page

```typescript
// app/blog/page.tsx
import { getBlogPosts } from '@/lib/cms';
import Link from 'next/link';
import Image from 'next/image';

export default async function BlogPage() {
  const { data: posts, pagination } = await getBlogPosts({ limit: 6 });

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8">Blog</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <article key={post._id} className="bg-white rounded-lg shadow-md overflow-hidden">
            {post.thumbnail && (
              <Image
                src={post.thumbnail}
                alt={post.title}
                width={400}
                height={200}
                className="w-full h-48 object-cover"
              />
            )}
            
            <div className="p-6">
              <div className="flex items-center gap-2 mb-2">
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {post.category}
                </span>
                {post.featured && (
                  <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
                    Featured
                  </span>
                )}
              </div>
              
              <h2 className="text-xl font-semibold mb-2">
                <Link href={`/blog/${post.slug}`} className="hover:text-blue-600">
                  {post.title}
                </Link>
              </h2>
              
              <p className="text-gray-600 mb-4">{post.excerpt}</p>
              
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{post.readTime} min read</span>
                <span>{post.views} views</span>
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
}
```

### 3. Create Dynamic Blog Post Pages

```typescript
// app/blog/[slug]/page.tsx
import { getBlogPost, getBlogPosts } from '@/lib/cms';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';

interface Props {
  params: { slug: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const { data: post } = await getBlogPost(params.slug);
    
    return {
      title: post.seoTitle || post.title,
      description: post.seoDescription || post.excerpt,
      keywords: post.seoKeywords?.join(', '),
      openGraph: {
        title: post.title,
        description: post.excerpt,
        images: post.thumbnail ? [post.thumbnail] : [],
      },
    };
  } catch {
    return {
      title: 'Post Not Found',
    };
  }
}

export async function generateStaticParams() {
  try {
    const { data: posts } = await getBlogPosts({ limit: 100 });
    return posts.map((post) => ({
      slug: post.slug,
    }));
  } catch {
    return [];
  }
}

export default async function BlogPostPage({ params }: Props) {
  try {
    const { data: post } = await getBlogPost(params.slug);
    
    return (
      <article className="container mx-auto px-4 py-8 max-w-4xl">
        <header className="mb-8">
          <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
          
          <div className="flex items-center gap-4 text-gray-600 mb-4">
            <span>By {post.author}</span>
            <span>•</span>
            <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
            <span>•</span>
            <span>{post.readTime} min read</span>
            <span>•</span>
            <span>{post.views} views</span>
          </div>
          
          <div className="flex gap-2">
            <span className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded">
              {post.category}
            </span>
            {post.tags.map((tag) => (
              <span key={tag} className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded">
                #{tag}
              </span>
            ))}
          </div>
        </header>
        
        {post.thumbnail && (
          <Image
            src={post.thumbnail}
            alt={post.title}
            width={800}
            height={400}
            className="w-full h-64 object-cover rounded-lg mb-8"
          />
        )}
        
        <div 
          className="prose prose-lg max-w-none"
          dangerouslySetInnerHTML={{ __html: post.content || '' }}
        />
      </article>
    );
  } catch {
    notFound();
  }
}
```

### 4. Environment Variables for Portfolio

Add to your portfolio's `.env.local`:

```env
# CMS Integration
NEXT_PUBLIC_CMS_URL=http://localhost:3002
```

For production:
```env
NEXT_PUBLIC_CMS_URL=https://your-cms-domain.com
```

## 🔄 Real-time Updates

To get real-time updates when content changes in the CMS, you can:

1. **Use ISR (Incremental Static Regeneration)**: Set `revalidate` in your fetch calls
2. **Webhook Integration**: Set up webhooks in the CMS to trigger rebuilds
3. **On-demand Revalidation**: Use Next.js on-demand revalidation API

## 🎨 Styling Blog Content

The CMS outputs HTML content. Make sure your portfolio has proper CSS for the content:

```css
/* Add to your global CSS */
.prose {
  /* Style for blog content */
}

.prose h1, .prose h2, .prose h3 {
  /* Heading styles */
}

.prose p {
  /* Paragraph styles */
}

.prose img {
  /* Image styles */
}

.prose a {
  /* Link styles */
}

.prose blockquote {
  /* Blockquote styles */
}

.prose code {
  /* Code styles */
}
```

## 🚀 Deployment Considerations

1. **CORS**: Make sure your CMS allows requests from your portfolio domain
2. **Environment Variables**: Update CMS URL for production
3. **Caching**: Implement proper caching strategies
4. **Error Handling**: Handle cases when CMS is unavailable

## 📝 Example Integration

Check the `uttam-portfolio` project for a complete example of how to integrate this CMS with your portfolio website.

---

This integration allows you to manage all your blog content through the CMS while keeping your portfolio website fast and SEO-optimized! 🎉
