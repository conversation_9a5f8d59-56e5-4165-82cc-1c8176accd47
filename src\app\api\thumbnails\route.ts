import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Thumbnail from "@/models/Thumbnail";
import { requireAuth } from "@/lib/auth";
import { withCors } from "@/lib/cors";
import slugify from "slugify";

// GET /api/thumbnails - Get all thumbnails with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const featured = searchParams.get("featured");
    const search = searchParams.get("search");
    const category = searchParams.get("category");

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (featured !== null) query.featured = featured === "true";
    if (category) query.category = category;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { tags: { $in: [new RegExp(search, "i")] } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get thumbnails with pagination
    const thumbnails = await Thumbnail.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await Thumbnail.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: thumbnails,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get thumbnails error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// POST /api/thumbnails - Create new thumbnail
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      videoId,
      title,
      description,
      category,
      tags = [],
      featured = false,
      status = "draft",
      order = 0,
    } = data;

    // Validate required fields
    if (!videoId || !title) {
      return NextResponse.json(
        { error: "Video ID and title are required" },
        { status: 400 }
      );
    }

    // Validate YouTube video ID format
    if (!/^[a-zA-Z0-9_-]{11}$/.test(videoId)) {
      return NextResponse.json(
        { error: "Invalid YouTube video ID format" },
        { status: 400 }
      );
    }

    // Check if video ID already exists
    const existingThumbnail = await Thumbnail.findOne({ videoId });
    if (existingThumbnail) {
      return NextResponse.json(
        { error: "A thumbnail with this video ID already exists" },
        { status: 400 }
      );
    }

    // Generate slug
    const baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    while (await Thumbnail.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create thumbnail
    const thumbnail = new Thumbnail({
      videoId,
      title,
      slug,
      description,
      category,
      tags,
      featured,
      status,
      order,
    });

    await thumbnail.save();

    return NextResponse.json(
      {
        success: true,
        data: thumbnail,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create thumbnail error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
