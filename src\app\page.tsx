'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [systemStatus, setSystemStatus] = useState<'checking' | 'ready' | 'setup-needed'>('checking');

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    try {
      const response = await fetch('/api/status');
      const data = await response.json();

      if (data.success && data.status.database === 'connected' && data.status.adminUser === 'exists') {
        setSystemStatus('ready');
      } else {
        setSystemStatus('setup-needed');
      }
    } catch (error) {
      setSystemStatus('setup-needed');
    }
  };

  useEffect(() => {
    if (!loading && systemStatus !== 'checking') {
      if (systemStatus === 'setup-needed') {
        router.push('/setup');
      } else if (user) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    }
  }, [user, loading, systemStatus, router]);

  if (loading || systemStatus === 'checking') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {systemStatus === 'checking' ? 'Checking system status...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return null;
}
