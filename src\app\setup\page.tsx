'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Database, User, Shield, Loader2 } from 'lucide-react';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  icon: React.ComponentType<any>;
  error?: string;
}

export default function SetupPage() {
  const [steps, setSteps] = useState<SetupStep[]>([
    {
      id: 'database',
      title: 'Database Connection',
      description: 'Connect to MongoDB database',
      status: 'pending',
      icon: Database,
    },
    {
      id: 'admin',
      title: 'Admin User',
      description: 'Create admin user account',
      status: 'pending',
      icon: User,
    },
    {
      id: 'auth',
      title: 'Authentication',
      description: 'Verify authentication system',
      status: 'pending',
      icon: Shield,
    },
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [completed, setCompleted] = useState(false);

  const updateStepStatus = (stepId: string, status: SetupStep['status'], error?: string) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, error } : step
    ));
  };

  const runSetup = async () => {
    setIsRunning(true);
    setCompleted(false);

    try {
      // Step 1: Initialize admin user (this will also test DB connection)
      updateStepStatus('database', 'running');
      updateStepStatus('admin', 'running');

      const response = await fetch('/api/init', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        updateStepStatus('database', 'success');
        updateStepStatus('admin', 'success');
        
        // Step 2: Test authentication
        updateStepStatus('auth', 'running');
        
        // Try to login with the created admin user
        const loginResponse = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: data.credentials.email,
            password: data.credentials.password,
          }),
        });

        if (loginResponse.ok) {
          updateStepStatus('auth', 'success');
          setCompleted(true);
        } else {
          updateStepStatus('auth', 'error', 'Authentication test failed');
        }
      } else {
        updateStepStatus('database', 'error', data.error || 'Database connection failed');
        updateStepStatus('admin', 'error', 'Failed to create admin user');
        updateStepStatus('auth', 'error', 'Cannot test authentication');
      }
    } catch (error) {
      console.error('Setup error:', error);
      updateStepStatus('database', 'error', 'Network error or MongoDB not running');
      updateStepStatus('admin', 'error', 'Setup failed');
      updateStepStatus('auth', 'error', 'Setup failed');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: SetupStep['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold">Portfolio CMS Setup</CardTitle>
          <CardDescription>
            Initialize your content management system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Prerequisites */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Prerequisites</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• MongoDB should be running on localhost:27017</li>
              <li>• Make sure your .env.local file is configured</li>
              <li>• Default admin credentials: <EMAIL> / admin123</li>
            </ul>
          </div>

          {/* Setup Steps */}
          <div className="space-y-4">
            {steps.map((step) => (
              <div key={step.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  {getStatusIcon(step.status)}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{step.title}</h3>
                  <p className="text-sm text-gray-600">{step.description}</p>
                  {step.error && (
                    <p className="text-sm text-red-600 mt-1">{step.error}</p>
                  )}
                </div>
                <div className="flex-shrink-0">
                  <step.icon className="h-6 w-6 text-gray-400" />
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4">
            <Button
              onClick={runSetup}
              disabled={isRunning}
              className="px-8"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Setting up...
                </>
              ) : (
                'Start Setup'
              )}
            </Button>

            {completed && (
              <Button
                variant="outline"
                onClick={() => window.location.href = '/login'}
              >
                Go to Login
              </Button>
            )}
          </div>

          {/* MongoDB Instructions */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-900 mb-2">MongoDB Setup</h3>
            <div className="text-sm text-yellow-800 space-y-2">
              <p>If you don't have MongoDB running, here are quick setup options:</p>
              <div className="bg-yellow-100 p-3 rounded font-mono text-xs">
                <p><strong>Option 1 - Local MongoDB:</strong></p>
                <p>1. Download from: https://www.mongodb.com/try/download/community</p>
                <p>2. Install and start the service</p>
                <p>3. Default connection: mongodb://localhost:27017</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded font-mono text-xs">
                <p><strong>Option 2 - MongoDB Atlas (Cloud):</strong></p>
                <p>1. Create free account at: https://cloud.mongodb.com</p>
                <p>2. Create cluster and get connection string</p>
                <p>3. Update MONGODB_URI in .env.local</p>
              </div>
            </div>
          </div>

          {/* Success Message */}
          {completed && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <h3 className="font-semibold text-green-900">Setup Complete!</h3>
              <p className="text-sm text-green-800 mt-1">
                Your CMS is ready to use. You can now login with the admin credentials.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
