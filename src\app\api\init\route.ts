import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import User from "@/models/User";
import { hashPassword } from "@/lib/auth";

export async function POST() {
  try {
    await connectDB();

    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    const adminPassword = process.env.ADMIN_PASSWORD || "admin123";

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: adminEmail });

    if (existingAdmin) {
      return NextResponse.json({
        success: true,
        message: "Admin user already exists",
        credentials: {
          email: adminEmail,
          password: adminPassword,
        },
      });
    }

    // Create admin user
    const hashedPassword = await hashPassword(adminPassword);

    const adminUser = new User({
      email: adminEmail,
      password: hashedPassword,
      name: "Admin User",
      role: "admin",
      isActive: true,
    });

    await adminUser.save();

    return NextResponse.json({
      success: true,
      message: "Admin user created successfully",
      credentials: {
        email: adminEmail,
        password: adminPassword,
      },
    });
  } catch (error) {
    console.error("Error initializing admin user:", error);
    return NextResponse.json(
      { error: "Failed to initialize admin user" },
      { status: 500 }
    );
  }
}
