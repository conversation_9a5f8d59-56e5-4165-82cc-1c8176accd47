'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Loader2, Play, Eye, Instagram, Youtube } from 'lucide-react';
import Link from 'next/link';

interface Reel {
  _id: string;
  id: string;
  title: string;
  description?: string;
  platform: 'instagram' | 'youtube';
  embedUrl?: string;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  createdAt: string;
  updatedAt: string;
}

export default function EditReelPage() {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    platform: 'instagram' as 'instagram' | 'youtube',
    embedUrl: '',
    featured: false,
    status: 'draft' as 'draft' | 'published' | 'archived',
    order: 0,
  });

  useEffect(() => {
    if (params.id) {
      fetchReel();
    }
  }, [params.id]);

  const fetchReel = async () => {
    try {
      const response = await fetch(`/api/reels/${params.id}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        const reel = data.data;
        setFormData({
          id: reel.id || '',
          title: reel.title || '',
          description: reel.description || '',
          platform: reel.platform || 'instagram',
          embedUrl: reel.embedUrl || '',
          featured: reel.featured || false,
          status: reel.status || 'draft',
          order: reel.order || 0,
        });
      } else {
        setError('Failed to fetch reel');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateEmbedUrl = (reelId: string) => {
    return `https://www.youtube.com/embed/${reelId}`;
  };

  const generateThumbnail = (reelId: string) => {
    return `https://img.youtube.com/vi/${reelId}/maxresdefault.jpg`;
  };

  const handleSubmit = async (status: 'draft' | 'published') => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const embedUrl = formData.embedUrl || generateEmbedUrl(formData.id);

      const response = await fetch(`/api/reels/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          embedUrl,
          platform: 'youtube',
          status,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Short ${status === 'published' ? 'published' : 'updated'} successfully!`);
        setTimeout(() => {
          router.push('/dashboard/reels');
        }, 1500);
      } else {
        setError(data.error || 'Failed to update short');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram className="h-4 w-4" />;
      case 'youtube':
        return <Youtube className="h-4 w-4" />;
      default:
        return <Play className="h-4 w-4" />;
    }
  };

  if (fetchLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading short...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/reels">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Shorts
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit YouTube Short</h1>
              <p className="text-gray-600">Update YouTube Short information</p>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Short Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="id">YouTube Video ID *</Label>
                  <Input
                    id="id"
                    value={formData.id}
                    onChange={(e) => handleInputChange('id', e.target.value)}
                    placeholder="e.g., dQw4w9WgXcQ"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    YouTube video ID from the Shorts URL
                  </p>
                </div>

                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter reel title..."
                    required
                  />
                </div>



                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter reel description..."
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="embedUrl">Custom Embed URL (Optional)</Label>
                  <Input
                    id="embedUrl"
                    value={formData.embedUrl}
                    onChange={(e) => handleInputChange('embedUrl', e.target.value)}
                    placeholder="Leave empty to auto-generate"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Auto-generated: {formData.id ? generateEmbedUrl(formData.platform, formData.id) : 'Enter reel ID to preview'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 xl:sticky xl:top-6 xl:h-fit">
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                  <Label htmlFor="featured">Featured Reel</Label>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={() => handleSubmit('draft')}
                    variant="outline"
                    className="w-full"
                    disabled={loading || !formData.title || !formData.id}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                    Save as Draft
                  </Button>

                  <Button
                    onClick={() => handleSubmit('published')}
                    className="w-full"
                    disabled={loading || !formData.title || !formData.id}
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Eye className="mr-2 h-4 w-4" />}
                    Update & Publish
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="aspect-[9/16] max-w-[200px] bg-gray-100 rounded-lg overflow-hidden">
                    {formData.id ? (
                      <img
                        src={generateThumbnail(formData.id)}
                        alt={formData.title || 'YouTube Shorts thumbnail'}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/images/placeholder.svg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Play className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-sm">
                        {formData.title || 'Reel Title'}
                      </h3>
                      <Badge variant="outline" className="text-xs">
                        {getPlatformIcon('youtube')}
                        <span className="ml-1">YouTube Shorts</span>
                      </Badge>
                    </div>

                    <p className="text-xs text-gray-600">
                      {formData.description || 'Reel description will appear here...'}
                    </p>

                    {formData.featured && (
                      <Badge className="text-xs bg-yellow-100 text-yellow-800">
                        Featured
                      </Badge>
                    )}

                    {formData.id && (
                      <p className="text-xs text-gray-400">
                        ID: {formData.id}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
