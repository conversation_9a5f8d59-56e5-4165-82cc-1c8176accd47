'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Star,
  Image as ImageIcon,
  Eye,
  Youtube
} from 'lucide-react';
import Image from 'next/image';

interface Thumbnail {
  _id: string;
  videoId: string;
  title: string;
  slug: string;
  description?: string;
  category?: string;
  tags: string[];
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  thumbnailUrl: string;
  embedUrl: string;
  createdAt: string;
  updatedAt: string;
}

export default function ThumbnailsPage() {
  const [thumbnails, setThumbnails] = useState<Thumbnail[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');

  useEffect(() => {
    fetchThumbnails();
  }, []);

  const fetchThumbnails = async () => {
    try {
      const response = await fetch('/api/thumbnails', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setThumbnails(data.data);
      }
    } catch (error) {
      console.error('Error fetching thumbnails:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this thumbnail?')) {
      return;
    }

    try {
      const response = await fetch(`/api/thumbnails/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        setThumbnails(thumbnails.filter(thumbnail => thumbnail._id !== id));
      }
    } catch (error) {
      console.error('Error deleting thumbnail:', error);
    }
  };

  const filteredThumbnails = thumbnails.filter(thumbnail => {
    const matchesSearch = thumbnail.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (thumbnail.description && thumbnail.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      thumbnail.videoId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || thumbnail.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || thumbnail.category === categoryFilter;
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      published: 'default',
      archived: 'outline',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const uniqueCategories = Array.from(new Set(thumbnails.map(t => t.category).filter(Boolean)));

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">YouTube Thumbnails</h1>
            <p className="text-gray-600">Manage your YouTube video thumbnails showcase</p>
          </div>
          <Link href="/dashboard/thumbnails/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Thumbnail
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Thumbnails</CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{thumbnails.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {thumbnails.filter(t => t.status === 'published').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Featured</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {thumbnails.filter(t => t.featured).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search thumbnails..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>

          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Categories</option>
            {uniqueCategories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Thumbnails Table */}
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Thumbnail</TableHead>
                <TableHead>Video ID</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Featured</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </TableCell>
                </TableRow>
              ) : filteredThumbnails.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No thumbnails found
                  </TableCell>
                </TableRow>
              ) : (
                filteredThumbnails.map((thumbnail) => (
                  <TableRow key={thumbnail._id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Image
                          src={thumbnail.thumbnailUrl}
                          alt={thumbnail.title}
                          width={80}
                          height={45}
                          className="w-20 h-11 object-cover rounded"
                        />
                        <div>
                          <div className="font-medium">{thumbnail.title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {thumbnail.description || 'No description'}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Youtube className="h-4 w-4 text-red-600" />
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {thumbnail.videoId}
                        </code>
                      </div>
                    </TableCell>
                    <TableCell>
                      {thumbnail.category && (
                        <Badge variant="outline">{thumbnail.category}</Badge>
                      )}
                    </TableCell>
                    <TableCell>{getStatusBadge(thumbnail.status)}</TableCell>
                    <TableCell>
                      {thumbnail.featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {formatDate(thumbnail.createdAt)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/thumbnails/${thumbnail._id}`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(thumbnail._id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Card>
      </div>
    </DashboardLayout>
  );
}
