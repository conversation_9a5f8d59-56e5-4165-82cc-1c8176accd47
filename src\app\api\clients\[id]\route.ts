import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Client from "@/models/Client";
import { requireAuth } from "@/lib/auth";
import { setCorsHeaders } from "@/lib/cors";
import slugify from "slugify";

// Utility to get [id] from the URL path
const extractId = (req: NextRequest) => {
  const parts = req.nextUrl.pathname.split("/");
  return parts[parts.length - 1]; // e.g., /api/clients/123 => "123"
};

// GET /api/clients/[id] - Get single client
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const id = extractId(request);

    const client = await Client.findOne({
      $or: [{ _id: id }, { slug: id }],
    }).lean();

    if (!client) {
      const response = NextResponse.json({ error: "Client not found" }, { status: 404 });
      setCorsHeaders(response, request.headers.get('origin'));
      return response;
    }

    const response = NextResponse.json({ success: true, data: client });
    setCorsHeaders(response, request.headers.get('origin'));
    return response;
  } catch (error) {
    console.error("Get client error:", error);
    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    setCorsHeaders(response, request.headers.get('origin'));
    return response;
  }
}

// PUT /api/clients/[id] - Update client
export const PUT = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const id = extractId(request);
    const data = await request.json();

    const client = await Client.findById(id);
    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    // Handle slug update if name changed
    if (data.name && data.name !== client.name) {
      const baseSlug = slugify(data.name, {
        lower: true,
        strict: true,
        remove: /[*+~.()'"!:@]/g,
      });

      let slug = baseSlug;
      let counter = 1;
      while (await Client.findOne({ slug, _id: { $ne: id } })) {
        slug = `${baseSlug}-${counter++}`;
      }
      data.slug = slug;
    }

    const updatedClient = await Client.findByIdAndUpdate(
      id,
      { ...data, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    return NextResponse.json({ success: true, data: updatedClient });
  } catch (error) {
    console.error("Update client error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/clients/[id] - Delete client
export const DELETE = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const id = extractId(request);

    const client = await Client.findById(id);
    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 });
    }

    await Client.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: "Client deleted successfully",
    });
  } catch (error) {
    console.error("Delete client error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
