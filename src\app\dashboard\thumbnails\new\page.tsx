'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Youtube, Eye } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export default function NewThumbnailPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [formData, setFormData] = useState({
    videoId: '',
    title: '',
    description: '',
    category: '',
    tags: '',
    featured: false,
    status: 'draft' as 'draft' | 'published' | 'archived',
    order: 0,
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Generate preview URL when video ID changes
    if (field === 'videoId' && value) {
      if (/^[a-zA-Z0-9_-]{11}$/.test(value)) {
        setPreviewUrl(`https://img.youtube.com/vi/${value}/maxresdefault.jpg`);
      } else {
        setPreviewUrl('');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const tagsArray = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      const response = await fetch('/api/thumbnails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          tags: tagsArray,
        }),
      });

      if (response.ok) {
        router.push('/dashboard/thumbnails');
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create thumbnail');
      }
    } catch (error) {
      console.error('Error creating thumbnail:', error);
      alert('Failed to create thumbnail');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/thumbnails">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Thumbnails
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">New Thumbnail</h1>
            <p className="text-gray-600">Add a new YouTube video thumbnail to your showcase</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Thumbnail Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="videoId">YouTube Video ID *</Label>
                  <div className="relative">
                    <Youtube className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="videoId"
                      placeholder="e.g., dQw4w9WgXcQ"
                      value={formData.videoId}
                      onChange={(e) => handleInputChange('videoId', e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                  <p className="text-sm text-gray-500">
                    Enter the 11-character YouTube video ID (found in the URL after v=)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    placeholder="Enter thumbnail title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter thumbnail description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    placeholder="e.g., Tutorial, Entertainment, Music"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">Tags</Label>
                  <Input
                    id="tags"
                    placeholder="Enter tags separated by commas"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    Separate multiple tags with commas
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="order">Display Order</Label>
                  <Input
                    id="order"
                    type="number"
                    placeholder="0"
                    value={formData.order}
                    onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                  />
                  <p className="text-sm text-gray-500">
                    Lower numbers appear first
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="featured"
                      checked={formData.featured}
                      onCheckedChange={(checked) => handleInputChange('featured', checked)}
                    />
                    <Label htmlFor="featured">Featured thumbnail</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Thumbnail'}
                  </Button>
                  <Link href="/dashboard/thumbnails">
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Preview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {previewUrl ? (
                <div className="space-y-4">
                  <div className="aspect-video w-full bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={previewUrl}
                      alt="Thumbnail preview"
                      width={640}
                      height={360}
                      className="w-full h-full object-cover"
                      onError={() => setPreviewUrl('')}
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">
                      {formData.title || 'Untitled'}
                    </h3>
                    {formData.description && (
                      <p className="text-gray-600 text-sm">
                        {formData.description}
                      </p>
                    )}
                    {formData.category && (
                      <div className="inline-block bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                        {formData.category}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="aspect-video w-full bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <Youtube className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Enter a valid YouTube video ID to see preview</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
