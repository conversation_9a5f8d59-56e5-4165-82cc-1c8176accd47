import mongoose, { Document, Schema } from 'mongoose';

export interface ITestimonial extends Document {
  name: string;
  role: string;
  company?: string;
  content: string;
  avatar?: string;
  rating: number;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  email?: string;
  linkedinUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

const TestimonialSchema = new Schema<ITestimonial>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  role: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  company: {
    type: String,
    trim: true,
    maxlength: 100,
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  avatar: {
    type: String,
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
    default: 5,
  },
  featured: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  order: {
    type: Number,
    default: 0,
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
  },
  linkedinUrl: {
    type: String,
    trim: true,
  },
}, {
  timestamps: true,
});

// Indexes
TestimonialSchema.index({ status: 1, order: 1 });
TestimonialSchema.index({ featured: 1 });
TestimonialSchema.index({ rating: -1 });

export default mongoose.models.Testimonial || mongoose.model<ITestimonial>('Testimonial', TestimonialSchema);
