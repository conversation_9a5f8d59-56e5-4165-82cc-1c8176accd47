import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Thumbnail from "@/models/Thumbnail";
import { requireAuth } from "@/lib/auth";
import slugify from "slugify";

// POST /api/thumbnails/migrate - Add slugs to existing thumbnails that don't have them
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    // Find all thumbnails that don't have slugs or have empty slugs
    const thumbnailsWithoutSlugs = await Thumbnail.find({
      $or: [
        { slug: { $exists: false } },
        { slug: null },
        { slug: "" }
      ]
    });

    let migratedCount = 0;
    let errors: string[] = [];

    for (const thumbnail of thumbnailsWithoutSlugs) {
      try {
        // Generate slug from title
        const baseSlug = slugify(thumbnail.title, {
          lower: true,
          strict: true,
          remove: /[*+~.()'"!:@]/g,
        });

        // Ensure unique slug
        let slug = baseSlug;
        let counter = 1;
        while (await Thumbnail.findOne({ slug, _id: { $ne: thumbnail._id } })) {
          slug = `${baseSlug}-${counter}`;
          counter++;
        }

        // Update the thumbnail with the new slug
        await Thumbnail.findByIdAndUpdate(thumbnail._id, { slug });
        migratedCount++;
      } catch (error) {
        console.error(`Error migrating thumbnail ${thumbnail._id}:`, error);
        errors.push(`Failed to migrate thumbnail "${thumbnail.title}": ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Migration completed. ${migratedCount} thumbnails updated.`,
      migratedCount,
      totalFound: thumbnailsWithoutSlugs.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Thumbnail migration error:", error);
    return NextResponse.json(
      { error: "Internal server error during migration" },
      { status: 500 }
    );
  }
});
