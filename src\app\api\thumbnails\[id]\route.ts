import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Thumbnail from "@/models/Thumbnail";
import { requireAuth } from "@/lib/auth";
import { withCors } from "@/lib/cors";
import slugify from "slugify";

// GET /api/thumbnails/[id] - Get single thumbnail
export const GET = withCors(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    await connectDB();

    const thumbnail = await Thumbnail.findById(params.id);

    if (!thumbnail) {
      return NextResponse.json(
        { error: "Thumbnail not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: thumbnail,
    });
  } catch (error) {
    console.error("Get thumbnail error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// PUT /api/thumbnails/[id] - Update thumbnail
export const PUT = requireAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      videoId,
      title,
      description,
      category,
      tags,
      featured,
      status,
      order,
    } = data;

    // Find existing thumbnail
    const thumbnail = await Thumbnail.findById(params.id);
    if (!thumbnail) {
      return NextResponse.json(
        { error: "Thumbnail not found" },
        { status: 404 }
      );
    }

    // Validate YouTube video ID format if provided
    if (videoId && !/^[a-zA-Z0-9_-]{11}$/.test(videoId)) {
      return NextResponse.json(
        { error: "Invalid YouTube video ID format" },
        { status: 400 }
      );
    }

    // Check if video ID already exists (excluding current thumbnail)
    if (videoId && videoId !== thumbnail.videoId) {
      const existingThumbnail = await Thumbnail.findOne({ 
        videoId, 
        _id: { $ne: params.id } 
      });
      if (existingThumbnail) {
        return NextResponse.json(
          { error: "A thumbnail with this video ID already exists" },
          { status: 400 }
        );
      }
    }

    // Generate new slug if title changed
    let slug = thumbnail.slug;
    if (title && title !== thumbnail.title) {
      const baseSlug = slugify(title, {
        lower: true,
        strict: true,
        remove: /[*+~.()'"!:@]/g,
      });

      // Ensure unique slug
      slug = baseSlug;
      let counter = 1;
      while (await Thumbnail.findOne({ slug, _id: { $ne: params.id } })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }
    }

    // Update thumbnail
    const updatedThumbnail = await Thumbnail.findByIdAndUpdate(
      params.id,
      {
        ...(videoId && { videoId }),
        ...(title && { title, slug }),
        ...(description !== undefined && { description }),
        ...(category !== undefined && { category }),
        ...(tags !== undefined && { tags }),
        ...(featured !== undefined && { featured }),
        ...(status && { status }),
        ...(order !== undefined && { order }),
      },
      { new: true, runValidators: true }
    );

    return NextResponse.json({
      success: true,
      data: updatedThumbnail,
    });
  } catch (error) {
    console.error("Update thumbnail error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/thumbnails/[id] - Delete thumbnail
export const DELETE = requireAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    await connectDB();

    const thumbnail = await Thumbnail.findByIdAndDelete(params.id);

    if (!thumbnail) {
      return NextResponse.json(
        { error: "Thumbnail not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Thumbnail deleted successfully",
    });
  } catch (error) {
    console.error("Delete thumbnail error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
